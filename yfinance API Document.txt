Install
$ pip install yfinance
Quick start
Showing a small sample of yfinance API, the full API is much bigger and covered in API Reference.

import yfinance as yf
dat = yf.Ticker("MSFT")
One ticker symbol

dat = yf.Ticker("MSFT")
dat.info
dat.calendar
dat.analyst_price_targets
dat.quarterly_income_stmt
dat.history(period='1mo')
dat.option_chain(dat.options[0]).calls
Multiple ticker symbols

tickers = yf.Tickers('MSFT AAPL GOOG')
tickers.tickers['MSFT'].info
yf.download(['MSFT', 'AAPL', 'GOOG'], period='1mo')
Funds

spy = yf.Ticker('SPY').funds_data
spy.description
spy.top_holdings

Logging
yfinance uses the logging module to handle messages. By default, only errors are logged.

If debugging, you can switch to debug mode with custom formatting using:

import yfinance as yf
yf.enable_debug_mode()

Config
yfinance has a new global config for sharing common values.

Proxy
Set proxy once in config, affects all yfinance data fetches.

import yfinance as yf
yf.set_config(proxy="PROXY_SERVER")

Caching
Smarter Scraping
Install the nospam package to cache API calls and reduce spam to Yahoo:

pip install yfinance[nospam]
To use a custom requests session, pass a session= argument to the Ticker constructor. This allows for caching calls to the API as well as a custom way to modify requests via the User-agent header.

import requests_cache
session = requests_cache.CachedSession('yfinance.cache')
session.headers['User-agent'] = 'my-program/1.0'
ticker = yf.Ticker('MSFT', session=session)

# The scraped response will be stored in the cache
ticker.actions
Combine requests_cache with rate-limiting to avoid triggering Yahoo’s rate-limiter/blocker that can corrupt data.

from requests import Session
from requests_cache import CacheMixin, SQLiteCache
from requests_ratelimiter import LimiterMixin, MemoryQueueBucket
from pyrate_limiter import Duration, RequestRate, Limiter
class CachedLimiterSession(CacheMixin, LimiterMixin, Session):
   pass

session = CachedLimiterSession(
   limiter=Limiter(RequestRate(2, Duration.SECOND*5)),  # max 2 requests per 5 seconds
   bucket_class=MemoryQueueBucket,
   backend=SQLiteCache("yfinance.cache"),
)
Persistent Cache
To reduce Yahoo, yfinance store some data locally: timezones to localize dates, and cookie. Cache location is:

Windows = C:/Users/<USER>/AppData/Local/py-yfinance

Linux = /home/<USER>/.cache/py-yfinance

MacOS = /Users/<USER>/Library/Caches/py-yfinance

You can direct cache to use a different location with set_tz_cache_location:

import yfinance as yf
yf.set_tz_cache_location("custom/cache/location")

API Reference
Overview
The yfinance package provides easy access to Yahoo! Finance’s API to retrieve market data. It includes classes and functions for downloading historical market data, accessing ticker information, managing cache, and more.

Public API
The following are the publicly available classes, and functions exposed by the yfinance package:

Ticker: Class for accessing single ticker data.

Tickers: Class for handling multiple tickers.

Market: Class for accessing market summary.

download: Function to download market data for multiple tickers.

Search: Class for accessing search results.

Lookup: Class for looking up tickers.

Sector: Domain class for accessing sector information.

Industry: Domain class for accessing industry information.

Market: Class for accessing market status & summary.

EquityQuery: Class to build equity query filters.

FundQuery: Class to build fund query filters.

screen: Run equity/fund queries.

enable_debug_mode: Function to enable debug mode for logging.

set_tz_cache_location: Function to set the timezone cache location.

Ticker
class yfinance.Ticker(ticker, session=None, proxy=<object object>)
Attributes

actions
analyst_price_targets
balance_sheet
balancesheet
basic_info
calendar
Returns a dictionary of events, earnings, and dividends for the ticker

capital_gains
cash_flow
cashflow
dividends
earnings
earnings_dates
earnings_estimate
earnings_history
eps_revisions
eps_trend
fast_info
financials
funds_data
growth_estimates
history_metadata
income_stmt
incomestmt
info
insider_purchases
insider_roster_holders
insider_transactions
institutional_holders
isin
major_holders
mutualfund_holders
news
options
quarterly_balance_sheet
quarterly_balancesheet
quarterly_cash_flow
quarterly_cashflow
quarterly_earnings
quarterly_financials
quarterly_income_stmt
quarterly_incomestmt
recommendations
recommendations_summary
revenue_estimate
sec_filings
shares
splits
sustainability
ttm_cash_flow
ttm_cashflow
ttm_financials
ttm_income_stmt
ttm_incomestmt
upgrades_downgrades
Methods

__init__(ticker, session=None, proxy=<object object>)
get_actions(proxy=<object object>, period='max') → Series
get_analyst_price_targets(proxy=<object object>) → dict
get_balance_sheet(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
Parameters
:
as_dict: bool
Return table as Python dict Default is False

pretty: bool
Format row names nicely for readability Default is False

freq: str
“yearly” or “quarterly” Default is “yearly”

get_balancesheet(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
get_calendar(proxy=<object object>) → dict
get_capital_gains(proxy=<object object>, period='max') → Series
get_cash_flow(proxy=<object object>, as_dict=False, pretty=False, freq='yearly') → DataFrame | dict
Parameters
:
as_dict: bool
Return table as Python dict Default is False

pretty: bool
Format row names nicely for readability Default is False

freq: str
“yearly” or “quarterly” Default is “yearly”

get_cashflow(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
get_dividends(proxy=<object object>, period='max') → Series
get_earnings(proxy=<object object>, as_dict=False, freq='yearly')
Parameters
:
as_dict: bool
Return table as Python dict Default is False

freq: str
“yearly” or “quarterly” or “trailing” Default is “yearly”

get_earnings_dates(limit=12, proxy=<object object>) → DataFrame | None
Get earning dates (future and historic)

Parameters
:
limit (int) – max amount of upcoming and recent earnings dates to return. Default value 12 should return next 4 quarters and last 8 quarters. Increase if more history is needed.

Returns
:
pd.DataFrame

get_earnings_estimate(proxy=<object object>, as_dict=False)
get_earnings_history(proxy=<object object>, as_dict=False)
get_eps_revisions(proxy=<object object>, as_dict=False)
Index: 0q +1q 0y +1y Columns: upLast7days upLast30days downLast7days downLast30days

get_eps_trend(proxy=<object object>, as_dict=False)
Index: 0q +1q 0y +1y Columns: current 7daysAgo 30daysAgo 60daysAgo 90daysAgo

get_fast_info(proxy=<object object>)
get_financials(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
get_funds_data(proxy=<object object>) → FundsData | None
get_growth_estimates(proxy=<object object>, as_dict=False)
Index: 0q +1q 0y +1y +5y -5y Columns: stock industry sector index

get_history_metadata(proxy=<object object>) → dict
get_income_stmt(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
Parameters
:
as_dict: bool
Return table as Python dict Default is False

pretty: bool
Format row names nicely for readability Default is False

freq: str
“yearly” or “quarterly” or “trailing” Default is “yearly”

get_incomestmt(proxy=<object object>, as_dict=False, pretty=False, freq='yearly')
get_info(proxy=<object object>) → dict
get_insider_purchases(proxy=<object object>, as_dict=False)
get_insider_roster_holders(proxy=<object object>, as_dict=False)
get_insider_transactions(proxy=<object object>, as_dict=False)
get_institutional_holders(proxy=<object object>, as_dict=False)
get_isin(proxy=<object object>) → str | None
get_major_holders(proxy=<object object>, as_dict=False)
get_mutualfund_holders(proxy=<object object>, as_dict=False)
get_news(count=10, tab='news', proxy=<object object>) → list
Allowed options for tab: “news”, “all”, “press releases

get_recommendations(proxy=<object object>, as_dict=False)
Returns a DataFrame with the recommendations Columns: period strongBuy buy hold sell strongSell

get_recommendations_summary(proxy=<object object>, as_dict=False)
get_revenue_estimate(proxy=<object object>, as_dict=False)
get_sec_filings(proxy=<object object>) → dict
get_shares(proxy=<object object>, as_dict=False) → DataFrame | dict
get_shares_full(start=None, end=None, proxy=<object object>)

Tickers
class yfinance.Tickers(tickers, session=None)
Methods

__init__(tickers, session=None)
download(period='1mo', interval='1d', start=None, end=None, prepost=False, actions=True, auto_adjust=True, repair=False, proxy=<object object>, threads=True, group_by='column', progress=True, timeout=10, **kwargs)
history(period='1mo', interval='1d', start=None, end=None, prepost=False, actions=True, auto_adjust=True, repair=False, proxy=<object object>, threads=True, group_by='column', progress=True, timeout=10, **kwargs)
news()

Market
class yfinance.Market(market: str, session=None, proxy=<object object>, timeout=30)
Attributes

status
summary
Methods

__init__(market: str, session=None, proxy=<object object>, timeout=30)

yfinance.download
yfinance.download(tickers, start=None, end=None, actions=False, threads=True, ignore_tz=None, group_by='column', auto_adjust=None, back_adjust=False, repair=False, keepna=False, progress=True, period='max', interval='1d', prepost=False, proxy=<object object>, rounding=False, timeout=10, session=None, multi_level_index=True) → DataFrame | None
Download yahoo tickers :Parameters:

tickersstr, list
List of tickers to download

periodstr
Valid periods: 1d,5d,1mo,3mo,6mo,1y,2y,5y,10y,ytd,max Either Use period parameter or use start and end

intervalstr
Valid intervals: 1m,2m,5m,15m,30m,60m,90m,1h,1d,5d,1wk,1mo,3mo Intraday data cannot extend last 60 days

start: str
Download start date string (YYYY-MM-DD) or _datetime, inclusive. Default is 99 years ago E.g. for start=”2020-01-01”, the first data point will be on “2020-01-01”

end: str
Download end date string (YYYY-MM-DD) or _datetime, exclusive. Default is now E.g. for end=”2023-01-01”, the last data point will be on “2022-12-31”

group_bystr
Group by ‘ticker’ or ‘column’ (default)

prepostbool
Include Pre and Post market data in results? Default is False

auto_adjust: bool
Adjust all OHLC automatically? Default is True

repair: bool
Detect currency unit 100x mixups and attempt repair Default is False

keepna: bool
Keep NaN rows returned by Yahoo? Default is False

actions: bool
Download dividend + stock splits data. Default is False

threads: bool / int
How many threads to use for mass downloading. Default is True

ignore_tz: bool
When combining from different timezones, ignore that part of datetime. Default depends on interval. Intraday = False. Day+ = True.

rounding: bool
Optional. Round values to 2 decimal places?

timeout: None or float
If not None stops waiting for a response after given number of seconds. (Can also be a fraction of a second e.g. 0.01)

session: None or Session
Optional. Pass your own session object to be used for all requests

multi_level_index: bool
Optional. Always return a MultiIndex DataFrame? Default is True

Search
class yfinance.Search(query, max_results=8, news_count=8, lists_count=8, include_cb=True, include_nav_links=False, include_research=False, include_cultural_assets=False, enable_fuzzy_query=False, recommended=8, session=None, proxy=<object object>, timeout=30, raise_errors=True)
Fetches and organizes search results from Yahoo Finance, including stock quotes and news articles.

Parameters
:
query – The search query (ticker symbol or company name).

max_results – Maximum number of stock quotes to return (default 8).

news_count – Number of news articles to include (default 8).

lists_count – Number of lists to include (default 8).

include_cb – Include the company breakdown (default True).

include_nav_links – Include the navigation links (default False).

include_research – Include the research reports (default False).

include_cultural_assets – Include the cultural assets (default False).

enable_fuzzy_query – Enable fuzzy search for typos (default False).

recommended – Recommended number of results to return (default 8).

session – Custom HTTP session for requests (default None).

timeout – Request timeout in seconds (default 30).

raise_errors – Raise exceptions on error (default True).

Attributes

all
filtered down version of response.

Type
:
Get all the results from the search results

lists
Get the lists from the search results.

nav
Get the navigation links from the search results.

news
Get the news from the search results.

quotes
Get the quotes from the search results.

research
Get the research reports from the search results.

response
Get the raw response from the search results.

Methods

__init__(query, max_results=8, news_count=8, lists_count=8, include_cb=True, include_nav_links=False, include_research=False, include_cultural_assets=False, enable_fuzzy_query=False, recommended=8, session=None, proxy=<object object>, timeout=30, raise_errors=True)
Fetches and organizes search results from Yahoo Finance, including stock quotes and news articles.

Parameters
:
query – The search query (ticker symbol or company name).

max_results – Maximum number of stock quotes to return (default 8).

news_count – Number of news articles to include (default 8).

lists_count – Number of lists to include (default 8).

include_cb – Include the company breakdown (default True).

include_nav_links – Include the navigation links (default False).

include_research – Include the research reports (default False).

include_cultural_assets – Include the cultural assets (default False).

enable_fuzzy_query – Enable fuzzy search for typos (default False).

recommended – Recommended number of results to return (default 8).

session – Custom HTTP session for requests (default None).

timeout – Request timeout in seconds (default 30).

raise_errors – Raise exceptions on error (default True).

search() → Search
Search using the query parameters defined in the constructor.

property all: dict[str, list]
filtered down version of response.

Type
:
Get all the results from the search results

property lists: list
Get the lists from the search results.

property nav: list
Get the navigation links from the search results.

property news: list
Get the news from the search results.

property quotes: list
Get the quotes from the search results.

property research: list
Get the research reports from the search results.

property response: dict
Get the raw response from the search results.

search() → Search
Search using the query parameters defined in the constructor.

Lookup
class yfinance.Lookup(query: str, session=None, proxy=None, timeout=30, raise_errors=True)
Fetches quote (ticker) lookups from Yahoo Finance.

Parameters
:
query (str) – The search query for financial data lookup.

session – Custom HTTP session for requests (default None).

proxy – Proxy settings for requests (default None).

timeout – Request timeout in seconds (default 30).

raise_errors – Raise exceptions on error (default True).

Attributes

all
Returns all available financial instruments.

cryptocurrency
Returns Cryptocurrencies related financial instruments.

currency
Returns Currencies related financial instruments.

etf
Returns ETFs related financial instruments.

future
Returns Futures related financial instruments.

index
Returns Indices related financial instruments.

mutualfund
Returns mutual funds related financial instruments.

stock
Returns stock related financial instruments.

Methods

__init__(query: str, session=None, proxy=None, timeout=30, raise_errors=True)
get_all(count=25) → DataFrame
Returns all available financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_cryptocurrency(count=25) → DataFrame
Returns Cryptocurrencies related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_currency(count=25) → DataFrame
Returns Currencies related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_etf(count=25) → DataFrame
Returns ETFs related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_future(count=25) → DataFrame
Returns Futures related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_index(count=25) → DataFrame
Returns Indices related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_mutualfund(count=25) → DataFrame
Returns mutual funds related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_stock(count=25) → DataFrame
Returns stock related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

property all: DataFrame
Returns all available financial instruments.

property cryptocurrency: DataFrame
Returns Cryptocurrencies related financial instruments.

property currency: DataFrame
Returns Currencies related financial instruments.

property etf: DataFrame
Returns ETFs related financial instruments.

property future: DataFrame
Returns Futures related financial instruments.

get_all(count=25) → DataFrame
Returns all available financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_cryptocurrency(count=25) → DataFrame
Returns Cryptocurrencies related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_currency(count=25) → DataFrame
Returns Currencies related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_etf(count=25) → DataFrame
Returns ETFs related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_future(count=25) → DataFrame
Returns Futures related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_index(count=25) → DataFrame
Returns Indices related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_mutualfund(count=25) → DataFrame
Returns mutual funds related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

get_stock(count=25) → DataFrame
Returns stock related financial instruments.

Parameters
:
count (int) – The number of results to retrieve.

property index: DataFrame
Returns Indices related financial instruments.

property mutualfund: DataFrame
Returns mutual funds related financial instruments.

property stock: DataFrame
Returns stock related financial instruments.

yfinance.enable_debug_mode
yfinance.enable_debug_mode()
